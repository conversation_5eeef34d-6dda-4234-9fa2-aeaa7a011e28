FROM python:3.11-slim

# Installa le dipendenze di sistema necessarie
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Imposta la directory di lavoro
WORKDIR /app

# Crea la directory di output
RUN mkdir -p /app/output

# Copia i file requirements
COPY requirements.txt .

# Installa le dipendenze Python
RUN pip install --no-cache-dir -r requirements.txt

# Copia i file dell'applicazione
COPY . .

# Comando di default
CMD ["python", "scraper.py"]
