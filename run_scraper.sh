#!/bin/bash

echo "🐳 Costruzione dell'immagine Docker..."
docker build -t amazon-scraper .

echo ""
echo "🚀 Avvio del container per estrarre i dati..."
docker run --rm -v "$(pwd)":/app/output amazon-scraper

echo ""
echo "✅ Processo completato!"
echo "📁 Il file Excel dovrebbe essere stato creato nella directory corrente."

# Verifica se il file è stato creato
if [ -f "confronto_idropulitrici.xlsx" ]; then
    echo "✅ File 'confronto_idropulitrici.xlsx' creato con successo!"
    echo "📊 Puoi aprirlo con Excel, LibreOffice Calc o Google Sheets"
else
    echo "❌ Il file Excel non è stato trovato. Controlla i log sopra per eventuali errori."
fi
