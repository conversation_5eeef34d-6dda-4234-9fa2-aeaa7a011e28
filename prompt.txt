Crea un file excel che contiene sulle righe i prodotti e sulle colonne le caratteristiche.
 - i prodotti devono essere presi dal file @product_links.txt
 - le caratteristiche le devi individuare in base alla collezione dei prodotti come ad esempio consumo, peso, dimensioni, costo e assicurati di avare le seguenti colonne:
    - la foto principale renderizzata quindi visibile nel file excel
    - il link
    - il brand
   le caratteristiche devono poter essere ordinabili quindi non devono avere nessun riferimento alle unità di misura (tieni l'unità di misura solo nell'intestazione)
 - metti più caratteristiche confrontabili possible

dettagli tecnici:
 - non installare niente sulla macchina host al più usa docker
 - swscrivi sempre tutto in italiano