#!/usr/bin/env python3
"""
Script per estrarre informazioni sui prodotti Amazon e creare un file Excel di confronto
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import re
import time
import random
from urllib.parse import urljoin
import json

class AmazonScraper:
    def __init__(self):
        self.session = requests.Session()
        # Headers per simulare un browser reale
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept-Language': 'it-IT,it;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(self.headers)
    
    def get_product_info(self, url):
        """Estrae informazioni da una pagina prodotto Amazon"""
        try:
            print(f"Elaborando: {url}")
            
            # Pausa casuale per evitare rate limiting
            time.sleep(random.uniform(1, 3))
            
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            product_info = {
                'Link': url,
                'Nome_Prodotto': self.extract_title(soup),
                'Brand': self.extract_brand(soup),
                'Prezzo': self.extract_price(soup),
                'Foto_Principale': self.extract_main_image(soup),
                'Valutazione': self.extract_rating(soup),
                'Numero_Recensioni': self.extract_review_count(soup),
                'Disponibilita': self.extract_availability(soup)
            }
            
            # Estrai caratteristiche tecniche specifiche per idropulitrici
            tech_specs = self.extract_technical_specs(soup)
            product_info.update(tech_specs)
            
            return product_info
            
        except Exception as e:
            print(f"Errore nell'elaborazione di {url}: {str(e)}")
            return {
                'Link': url,
                'Nome_Prodotto': 'Errore nel caricamento',
                'Brand': '',
                'Prezzo': '',
                'Foto_Principale': '',
                'Valutazione': '',
                'Numero_Recensioni': '',
                'Disponibilita': '',
                'Errore': str(e)
            }
    
    def extract_title(self, soup):
        """Estrae il titolo del prodotto"""
        selectors = [
            '#productTitle',
            '.product-title',
            'h1.a-size-large'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text().strip()
        return ''
    
    def extract_brand(self, soup):
        """Estrae il brand del prodotto"""
        # Cerca il brand in vari modi
        selectors = [
            'a#bylineInfo',
            '.a-link-normal[data-brand]',
            'span.a-size-base.po-break-word',
            'tr.a-spacing-small td.a-span9 span'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                text = element.get_text().strip()
                if 'Visita lo store di' in text:
                    return text.replace('Visita lo store di', '').strip()
                elif 'Brand:' in text:
                    return text.replace('Brand:', '').strip()
                return text
        
        # Cerca nelle specifiche tecniche
        feature_bullets = soup.select('.feature-bullet ul li span')
        for bullet in feature_bullets:
            text = bullet.get_text().strip()
            if text.startswith('Brand:') or text.startswith('Marca:'):
                return text.split(':', 1)[1].strip()
        
        return ''
    
    def extract_price(self, soup):
        """Estrae il prezzo del prodotto"""
        selectors = [
            '.a-price-whole',
            '.a-offscreen',
            '.a-price .a-offscreen',
            '#priceblock_dealprice',
            '#priceblock_ourprice'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                price_text = element.get_text().strip()
                # Pulisci il prezzo
                price_match = re.search(r'[\d,]+(?:\.\d{2})?', price_text.replace('€', ''))
                if price_match:
                    return f"€{price_match.group()}"
        return ''
    
    def extract_main_image(self, soup):
        """Estrae l'URL dell'immagine principale"""
        selectors = [
            '#landingImage',
            '.a-dynamic-image',
            '#imgBlkFront'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                src = element.get('src') or element.get('data-src')
                if src:
                    return src
        return ''
    
    def extract_rating(self, soup):
        """Estrae la valutazione del prodotto"""
        rating_element = soup.select_one('.a-icon-alt')
        if rating_element:
            rating_text = rating_element.get_text()
            rating_match = re.search(r'(\d+,?\d*)', rating_text)
            if rating_match:
                return rating_match.group(1)
        return ''
    
    def extract_review_count(self, soup):
        """Estrae il numero di recensioni"""
        selectors = [
            '#acrCustomerReviewText',
            '.a-link-normal[href*="reviews"] span'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                text = element.get_text()
                count_match = re.search(r'([\d.]+)', text.replace(',', '.'))
                if count_match:
                    return count_match.group(1)
        return ''
    
    def extract_availability(self, soup):
        """Estrae informazioni sulla disponibilità"""
        selectors = [
            '#availability span',
            '.a-color-success',
            '.a-color-state'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text().strip()
        return ''
    
    def extract_technical_specs(self, soup):
        """Estrae specifiche tecniche specifiche per idropulitrici"""
        specs = {
            'Potenza_W': '',
            'Pressione_Bar': '',
            'Portata_L_h': '',
            'Peso_Kg': '',
            'Dimensioni': '',
            'Lunghezza_Cavo_m': '',
            'Lunghezza_Tubo_m': '',
            'Serbatoio_Detergente': '',
            'Tipo_Alimentazione': '',
            'Accessori_Inclusi': ''
        }
        
        # Cerca nelle feature bullets
        feature_bullets = soup.select('.feature-bullet ul li span, .a-unordered-list .a-list-item')
        for bullet in feature_bullets:
            text = bullet.get_text().strip().lower()
            
            # Potenza
            power_match = re.search(r'(\d+)\s*w(?:att)?', text)
            if power_match and not specs['Potenza_W']:
                specs['Potenza_W'] = f"{power_match.group(1)}W"
            
            # Pressione
            pressure_match = re.search(r'(\d+)\s*bar', text)
            if pressure_match and not specs['Pressione_Bar']:
                specs['Pressione_Bar'] = f"{pressure_match.group(1)} bar"
            
            # Portata
            flow_match = re.search(r'(\d+)\s*l/h', text)
            if flow_match and not specs['Portata_L_h']:
                specs['Portata_L_h'] = f"{flow_match.group(1)} L/h"
            
            # Peso
            weight_match = re.search(r'(\d+(?:,\d+)?)\s*kg', text)
            if weight_match and not specs['Peso_Kg']:
                specs['Peso_Kg'] = f"{weight_match.group(1)} kg"
            
            # Lunghezza cavo
            cable_match = re.search(r'cavo.*?(\d+)\s*m', text)
            if cable_match and not specs['Lunghezza_Cavo_m']:
                specs['Lunghezza_Cavo_m'] = f"{cable_match.group(1)}m"
            
            # Lunghezza tubo
            hose_match = re.search(r'tubo.*?(\d+)\s*m', text)
            if hose_match and not specs['Lunghezza_Tubo_m']:
                specs['Lunghezza_Tubo_m'] = f"{hose_match.group(1)}m"
        
        # Cerca nella tabella delle specifiche tecniche
        tech_table = soup.select('.a-normal .a-spacing-micro')
        for row in tech_table:
            cells = row.select('td')
            if len(cells) >= 2:
                key = cells[0].get_text().strip().lower()
                value = cells[1].get_text().strip()
                
                if 'potenza' in key and not specs['Potenza_W']:
                    power_match = re.search(r'(\d+)\s*w', value.lower())
                    if power_match:
                        specs['Potenza_W'] = f"{power_match.group(1)}W"
                
                elif 'pressione' in key and not specs['Pressione_Bar']:
                    pressure_match = re.search(r'(\d+)\s*bar', value.lower())
                    if pressure_match:
                        specs['Pressione_Bar'] = f"{pressure_match.group(1)} bar"
                
                elif 'peso' in key and not specs['Peso_Kg']:
                    weight_match = re.search(r'(\d+(?:,\d+)?)\s*kg', value.lower())
                    if weight_match:
                        specs['Peso_Kg'] = f"{weight_match.group(1)} kg"
                
                elif 'dimensioni' in key and not specs['Dimensioni']:
                    specs['Dimensioni'] = value
        
        return specs

def main():
    """Funzione principale"""
    print("🚀 Avvio del scraper per idropulitrici Amazon")
    
    # Leggi i link dal file
    try:
        with open('product_links.txt', 'r', encoding='utf-8') as f:
            links = [line.strip() for line in f if line.strip() and line.strip().startswith('http')]
    except FileNotFoundError:
        print("❌ File product_links.txt non trovato!")
        return
    
    print(f"📋 Trovati {len(links)} prodotti da elaborare")
    
    scraper = AmazonScraper()
    products_data = []
    
    for i, link in enumerate(links, 1):
        print(f"\n📦 Elaborazione prodotto {i}/{len(links)}")
        product_info = scraper.get_product_info(link)
        products_data.append(product_info)
    
    # Crea DataFrame
    df = pd.DataFrame(products_data)
    
    # Riordina le colonne per una migliore leggibilità
    column_order = [
        'Nome_Prodotto', 'Brand', 'Prezzo', 'Valutazione', 'Numero_Recensioni',
        'Potenza_W', 'Pressione_Bar', 'Portata_L_h', 'Peso_Kg', 'Dimensioni',
        'Lunghezza_Cavo_m', 'Lunghezza_Tubo_m', 'Serbatoio_Detergente',
        'Tipo_Alimentazione', 'Accessori_Inclusi', 'Disponibilita',
        'Foto_Principale', 'Link'
    ]
    
    # Riordina le colonne mantenendo quelle esistenti
    existing_columns = [col for col in column_order if col in df.columns]
    other_columns = [col for col in df.columns if col not in column_order]
    final_columns = existing_columns + other_columns
    
    df = df[final_columns]
    
    # Salva in Excel
    output_file = '/app/output/confronto_idropulitrici.xlsx'
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Confronto Idropulitrici', index=False)
        
        # Formatta il foglio
        worksheet = writer.sheets['Confronto Idropulitrici']
        
        # Imposta larghezza colonne
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    print(f"\n✅ File Excel creato: {output_file}")
    print(f"📊 Elaborati {len(products_data)} prodotti")
    print("\n📋 Colonne incluse nel confronto:")
    for col in df.columns:
        print(f"  • {col}")

if __name__ == "__main__":
    main()
