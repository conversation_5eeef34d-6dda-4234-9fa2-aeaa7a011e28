# syntax=docker/dockerfile:1
FROM python:3.11-slim

# Install system deps for lxml
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
       build-essential \
       libxml2-dev \
       libxslt1-dev \
       curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY docker/requirements.txt /app/requirements.txt
RUN pip install --no-cache-dir -r /app/requirements.txt

COPY scrape_amazon.py /app/scrape_amazon.py

# Default command reads product_links.txt from a mounted volume and writes the Excel to the same volume
ENTRYPOINT ["python", "/app/scrape_amazon.py"]

