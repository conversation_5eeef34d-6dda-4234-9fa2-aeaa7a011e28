# 🔧 Confronto Idropulitrici Amazon

Questo progetto estrae automaticamente le informazioni sui prodotti Amazon dal file `product_links.txt` e crea un file Excel di confronto con tutte le caratteristiche tecniche.

## 📋 Caratteristiche Estratte

Il file Excel conterrà le seguenti colonne:

### 📊 Informazioni Base
- **Nome_Prodotto**: Nome completo del prodotto
- **Brand**: Marca del prodotto
- **Prezzo**: Prezzo attuale su Amazon
- **Valutazione**: Stelle di valutazione
- **Numero_Recensioni**: Numero di recensioni
- **Foto_Principale**: URL dell'immagine principale
- **Link**: Link diretto al prodotto

### ⚙️ Specifiche Tecniche
- **Potenza_W**: Potenza in Watt
- **Pressione_Bar**: Pressione massima in Bar
- **Portata_L_h**: Portata in Litri/ora
- **Peso_Kg**: Peso del prodotto
- **Dimensioni**: Dimensioni del prodotto
- **Lunghezza_Cavo_m**: Lunghezza del cavo elettrico
- **Lunghezza_Tubo_m**: Lunghezza del tubo
- **Serbatoio_Detergente**: Capacità serbatoio detergente
- **Tipo_Alimentazione**: Tipo di alimentazione
- **Accessori_Inclusi**: Lista degli accessori inclusi
- **Disponibilita**: Stato di disponibilità

## 🚀 Come Utilizzare

### Prerequisiti
- Docker installato sul sistema
- File `product_links.txt` con i link dei prodotti Amazon

### Esecuzione
```bash
# Esegui lo script automatico
./run_scraper.sh
```

### Esecuzione Manuale
Se preferisci eseguire i comandi manualmente:

```bash
# Costruisci l'immagine Docker
docker build -t amazon-scraper .

# Esegui il container
docker run --rm -v "$(pwd)":/app/output amazon-scraper
```

## 📁 File Generati

- `confronto_idropulitrici.xlsx`: File Excel con il confronto completo

## 🔧 Personalizzazione

Per modificare le caratteristiche estratte, modifica la funzione `extract_technical_specs()` nel file `scraper.py`.

## ⚠️ Note Importanti

- Lo script include pause casuali tra le richieste per rispettare i limiti di Amazon
- Alcuni prodotti potrebbero non avere tutte le informazioni disponibili
- Il processo può richiedere alcuni minuti a seconda del numero di prodotti

## 🐛 Risoluzione Problemi

Se il file Excel non viene creato:
1. Verifica che Docker sia installato e funzionante
2. Controlla che il file `product_links.txt` contenga link validi
3. Verifica i log del container per eventuali errori

## 📊 Formato Output

Il file Excel sarà formattato con:
- Colonne auto-ridimensionate
- Intestazioni chiare in italiano
- Dati organizzati per facile confronto
